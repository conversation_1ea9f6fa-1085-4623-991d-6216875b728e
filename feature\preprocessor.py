#!/usr/bin/env python3
# -*- coding: UTF-8 -*-
###########################################################################
# Copyright © 1998 - 2025 Tencent. All Rights Reserved.
###########################################################################

"""
Author: Tencent AI Arena Authors

"""

import numpy as np
import math
from agent_ppo.feature.definition import RelativeDistance, RelativeDirection, DirectionAngles, reward_process


def norm(v, max_v, min_v=0):
    v = np.maximum(np.minimum(max_v, v), min_v)
    return (v - min_v) / (max_v - min_v)


class Preprocessor:
    def __init__(self) -> None:
        # 从8修改成16，代表8个移动维度，8个闪现维度
        self.move_action_num = 16
        self.reset()

    def reset(self):
        self.step_no = 0
        self.cur_pos = (0, 0)
        self.cur_pos_norm = np.array((0, 0))
        self.end_pos = None
        self.is_end_pos_found = False
        self.history_pos = []
        self.bad_move_ids = set()
        # 闪现是否可用，初始化为True
        self.is_flashed = True
        # 初始化记忆矩阵
        self.global_memory_map = np.zeros((128,128), dtype=np.float32)
        self.local_memory_map = np.zeros((11,11), dtype=np.float32)

        # 初始化organs特征系统
        self._reset_organs_features()

    def _reset_organs_features(self):
        """重置organs原始特征"""
        # 原始organs特征矩阵：每个organ的正确编码特征
        # 最多15个organs，每个organ包含：
        # - 3维 sub_type one-hot: [is_treasure, is_buff, is_end]
        # - 3维 status one-hot: [is_out_of_sight, is_cooling, is_available]
        # - 2维 归一化位置: [norm_pos_x, norm_pos_z]
        # - 1维 归一化冷却: [norm_cooldown]
        # - 6维 relative_distance one-hot: [VerySmall, Small, Medium, Large, VeryLarge, Unknown]
        # - 9维 relative_direction one-hot: [North, NorthEast, East, SouthEast, South, SouthWest, West, NorthWest, Unknown]
        # 总计24维每个organ
        self.organs_features = np.zeros((15, 24), dtype=np.float32)

    def _generate_local_flags(self, organs):
        """
        根据英雄当前位置和视野内的物件生成11x11的局部地图标志
        status == 1 的物件必然在视野内
        """
        # 初始化11x11的标志矩阵
        self.treasure_flag = np.zeros(11 * 11, dtype=np.float32)
        self.end_flag = np.zeros(11 * 11, dtype=np.float32)
        self.obstacle_flag = np.zeros(11 * 11, dtype=np.float32)
        self.buff_flag = np.zeros(11 * 11, dtype=np.float32)

        # 英雄当前位置
        hero_x, hero_z = self.cur_pos

        # 遍历所有organs
        for organ in organs:
            # 获取物件信息
            sub_type = organ["sub_type"]  # 1=宝箱, 2=加速buff, 3=起点, 4=终点
            status = organ["status"]      # 0=不可获取, 1=可获取, -1=视野外

            # 只处理可获取的物件（status == 1 必然在视野内）
            if status != 1:
                continue

            # 获取物件位置
            pos_x = organ.get("pos", {}).get("x", -1)
            pos_z = organ.get("pos", {}).get("z", -1)

            if pos_x == -1 or pos_z == -1:
                continue

            # 计算相对于英雄的位置
            rel_x = pos_x - hero_x
            rel_z = pos_z - hero_z

            # 转换为11x11矩阵的索引（英雄在中心位置(5,5)）
            local_x = rel_x + 5
            local_z = rel_z + 5

            # 确保坐标在11x11范围内
            if 0 <= local_x < 11 and 0 <= local_z < 11:
                # 转换为flatten后的索引
                flat_idx = local_z * 11 + local_x

                # 根据sub_type设置对应的标志
                if sub_type == 1:  # 宝箱
                    self.treasure_flag[flat_idx] = 1.0
                elif sub_type == 2:  # 加速buff
                    self.buff_flag[flat_idx] = 1.0
                elif sub_type == 4:  # 终点
                    self.end_flag[flat_idx] = 1.0

    def memory_update(self, cur_pos):
        """
        记忆矩阵更新
        """
        # 全局记忆矩阵
        x,z = cur_pos
        z = 127 - z
        current_value = self.global_memory_map[z, x]
        self.global_memory_map[z, x] = min(1.0, current_value + 0.1)

        # 局部记忆矩阵
        # 计算在全局地图上的源区域边界
        src_top = max(0, z - 5)
        src_bottom = min(128, z + 6)
        src_left = max(0, x - 5)
        src_right = min(128, x + 6)

        # 计算在局部地图上的目标区域边界
        dst_top = src_top - (z - 5)
        dst_bottom = src_bottom - (z - 5)
        dst_left = src_left - (x - 5)
        dst_right = src_right - (x - 5)

        # 从全局地图复制有效区域到局部地图
        self.local_memory_map[dst_top:dst_bottom, dst_left:dst_right] = self.global_memory_map[src_top:src_bottom, src_left:src_right]
        self.memory_flag = self.local_memory_map.flatten()

    def _get_pos_feature(self, found, cur_pos, target_pos):
        relative_pos = tuple(y - x for x, y in zip(cur_pos, target_pos))
        dist = np.linalg.norm(relative_pos)
        target_pos_norm = norm(target_pos, 128, -128)
        feature = np.array(
            (
                found,
                norm(relative_pos[0] / max(dist, 1e-4), 1, -1),
                norm(relative_pos[1] / max(dist, 1e-4), 1, -1),
                target_pos_norm[0],
                target_pos_norm[1],
                norm(dist, 1.41 * 128),
            ),
        )
        return feature

    def pb2struct(self, frame_state, last_action):
        obs, _ = frame_state
        self.step_no = obs["frame_state"]["step_no"]

        hero = obs["frame_state"]["heroes"][0]
        map_info = obs["map_info"]  # 解析map_info，暂未使用
        
        # 闪现是否可用 -- 新增一个字段来代表闪现是否可用，默认初始化的时候可以为True
        if hero['talent']['status'] == 0:
            self.is_flashed = False

        # 获取当前位置
        self.cur_pos = (hero["pos"]["x"], hero["pos"]["z"])

        # 生成基于英雄视野的11x11局部地图标志
        self._generate_local_flags(obs["frame_state"]["organs"])



        # 更新记忆矩阵
        self.memory_update(self.cur_pos)
        '''
        message RealmOrgan {
        int32 sub_type = 1; // 物件类型，1代表宝箱, 2代表加速buff,3代表起点,4代表终点
        int32 config_id = 2; // 物件id 0代表buff，1~13代表宝箱 21代表起点, 22代表终点
        int32 status = 3; // 0表示不可获取，1表示可获取, -1表示视野外
        Position pos = 4; // 物件位置坐标
        int32 cooldown = 5;                // 物件剩余冷却时间
        RelativePosition relative_pos = 6; // 物件相对位置
        }
        '''

        # 重置organs特征矩阵
        self.organs_features.fill(0)  # 全部初始化为0

        # 遍历organs数据，进行正确的特征编码
        for organ in obs["frame_state"]["organs"]:
            config_id = organ["config_id"]
            sub_type = organ["sub_type"]
            status = organ["status"]

            # 确定存储索引
            if sub_type == 4:  # 终点
                organ_idx = 14
            elif 0 <= config_id < 14:  # 宝箱和buff
                organ_idx = config_id
            else:
                continue  # 跳过无效的organ

            # 构建正确编码的特征向量
            feature_vector = np.zeros(24, dtype=np.float32)

            # 1. sub_type one-hot编码 (3维)
            if sub_type == 1:  # treasure
                feature_vector[0] = 1.0
            elif sub_type == 2:  # buff
                feature_vector[1] = 1.0
            elif sub_type == 4:  # end
                feature_vector[2] = 1.0

            # 2. status one-hot编码 (3维)
            if status == -1:  # 视野外
                feature_vector[3] = 1.0
            elif status == 0:  # 冷却中
                feature_vector[4] = 1.0
            elif status == 1:  # 可获取
                feature_vector[5] = 1.0

            # 3. 归一化位置 (2维)
            pos_x = organ["pos"]["x"]
            pos_z = organ["pos"]["z"]
            if pos_x != -1 and pos_z != -1:
                feature_vector[6] = pos_x / 128.0  # 归一化到[0,1]
                feature_vector[7] = pos_z / 128.0  # 归一化到[0,1]
            # 如果位置未知，保持为0

            # 4. 归一化冷却时间 (1维)
            cooldown = organ['cooldown']
            feature_vector[8] = min(cooldown / 100.0, 1.0)  # 归一化到[0,1]，假设最大冷却100

            # 5. relative_distance one-hot编码 (6维)
            rel_dist = organ['relative_pos']['l2_distance']
            dist_mapping = {
                "VerySmall": 10,
                "Small": 11,
                "Medium": 12,
                "Large": 13,
                "VeryLarge": 14
            }
            if rel_dist in dist_mapping:
                feature_vector[dist_mapping[rel_dist]] = 1.0
            else:
                feature_vector[9] = 1.0  # Unknown

            # 6. relative_direction one-hot编码 (9维)
            rel_dir = organ['relative_pos']['direction']
            dir_mapping = {
                "North": 15,
                "NorthEast": 16,
                "East": 17,
                "SouthEast": 18,
                "South": 19,
                "SouthWest": 20,
                "West": 21,
                "NorthWest": 22
            }
            if rel_dir in dir_mapping:
                feature_vector[dir_mapping[rel_dir]] = 1.0
            else:
                feature_vector[23] = 1.0  # Unknown

            # 存储编码后的特征
            self.organs_features[organ_idx] = feature_vector




        # History position
        # 历史位置
        self.history_pos.append(self.cur_pos)
        if len(self.history_pos) > 10:
            self.history_pos.pop(0)

            

        self.last_pos_norm = self.cur_pos_norm
        self.cur_pos_norm = norm(self.cur_pos, 128, -128)

        # History position feature
        # 历史位置特征
        self.feature_history_pos = self._get_pos_feature(1, self.cur_pos, self.history_pos[0])

        self.move_usable = True
        self.last_action = last_action

    def process(self, frame_state, last_action):
        self.pb2struct(frame_state, last_action)

        # 1. 準備【純淨的】向量特徵 (Purified Vector Feature)
        vector_feature = np.concatenate([
            self.cur_pos_norm,                     # 2維
            self.feature_history_pos,              # 6維
            self.organs_features.flatten(),        # 360維
        ])  # 新的總維度: 368

        # 2. 準備【空間結構的】地圖特徵 (Spatial Map Feature)
        #    注意：不再 flatten()！保持 (11, 11) 的形狀
        map_features = np.stack([
            self.treasure_flag.reshape(11, 11),
            self.obstacle_flag.reshape(11, 11),
            self.buff_flag.reshape(11, 11),
            self.end_flag.reshape(11, 11),
            self.local_memory_map
        ], axis=0)  # 輸出形狀: (5, 11, 11)

        # 3. 準備合法動作
        legal_action = self.get_legal_action()

        # 4. 準備獎勵
        reward = [-0.001 + min(0.001, 0.05 * self.feature_history_pos[-1])]

        # 返回清晰分離的四個部分
        return (
            vector_feature,  # np.array, shape (368,)
            map_features,    # np.array, shape (5, 11, 11)
            legal_action,
            reward,
        )

    def get_legal_action(self):
        # if last_action is move and current position is the same as last position, add this action to bad_move_ids
        # 如果上一步的动作是移动，且当前位置与上一步位置相同，则将该动作加入到bad_move_ids中

        legal_action = [False] * self.move_action_num
        
        # 添加闪现的合法性
        if self.is_flashed:
            legal_action[8:] = [True] * 8
        else:
            legal_action[8:] = [False] * 8

        if (
            abs(self.cur_pos_norm[0] - self.last_pos_norm[0]) < 0.001
            and abs(self.cur_pos_norm[1] - self.last_pos_norm[1]) < 0.001
            and self.last_action > -1
        ):
            self.bad_move_ids.add(self.last_action)
        else:
            self.bad_move_ids = set()

        legal_action = [self.move_usable] * self.move_action_num
        for move_id in self.bad_move_ids:
            legal_action[move_id] = 0

        if self.move_usable not in legal_action:
            self.bad_move_ids = set()
            return [self.move_usable] * self.move_action_num

            
        return legal_action
